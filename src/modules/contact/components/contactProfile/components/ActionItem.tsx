import React from 'react'
import * as SharedStyled from '../../../../../styles/styled'
import { RoundButton } from '../../../../../shared/components/button/style'
import { dayjsFormat, getEnumValue } from '../../../../../shared/helpers/util'
import EditIcon from '../../../../../assets/newIcons/edit.svg'
import Pill from './Pill'

interface I_Action {
  type: string
  body: string
  completedBy: string
  assignTo?: string
  due: string
  _id?: string
  oppId?: string
  PO?: string
  num?: string
  contactId?: string
  stageGroup?: string
}

interface I_ActionItemProps {
  action: I_Action
  isNewAction: boolean
  todoCheckObj: { [key: string]: boolean }
  hasTodoCheckObj: boolean
  salesPersonDrop: any[]
  allActionData: {
    nextAction: I_Action[]
    history: I_Action[]
  }
  onTodoCheckChange: (actionId: string, checked: boolean) => void
  onEditTodo: (action: I_Action) => void
}

const ActionItem: React.FC<I_ActionItemProps> = ({
  action,
  isNewAction,
  todoCheckObj,
  hasTodoCheckObj,
  salesPersonDrop,
  allActionData,
  onTodoCheckChange,
  onEditTodo,
}) => {
  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation()

    if (todoCheckObj[action?._id!]) {
      onTodoCheckChange(action?._id!, false)
      return
    }

    onTodoCheckChange(action?._id!, e.target.checked)
  }

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onEditTodo(action)
  }

  return (
    <div className="todo-container" key={action?._id}>
      <SharedStyled.FlexRow margin="20px 0 0 0" justifyContent="space-between">
        <SharedStyled.FlexRow alignItems="flex-start">
          <input
            key={action?._id}
            type="radio"
            disabled={isNewAction}
            name="todoAction"
            onChange={handleRadioChange}
            checked={todoCheckObj[action?._id!] ?? false}
            style={{ cursor: 'pointer' }}
          />

          <div className="checkbox-item">
            <div className={todoCheckObj?.[action?._id!] ? 'strike' : ''}>
              <SharedStyled.FlexCol gap="2px">
                <SharedStyled.TooltipContainer
                  width="300px"
                  positionLeft="0"
                  style={{
                    maxWidth: '400px',
                  }}
                  positionBottom="0"
                  positionLeftDecs="100px"
                  positionBottomDecs="25px"
                >
                  <span className="tooltip-content">{action?.body}</span>
                  <SharedStyled.Text
                    as={'p'}
                    fontSize="14px"
                    fontWeight="bold"
                    style={{
                      maxWidth: '400px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {action?.body}
                  </SharedStyled.Text>
                </SharedStyled.TooltipContainer>
                <div>
                  <SharedStyled.Text fontSize="12px">
                    <>{action?.type} on </>
                  </SharedStyled.Text>
                  {action?.due ? (
                    <SharedStyled.Text fontSize="12px">
                      <>
                        {dayjsFormat(action?.due, 'M/D/YY')} @ {dayjsFormat(action?.due, 'h:mm a')}
                      </>
                    </SharedStyled.Text>
                  ) : (
                    ''
                  )}
                </div>

                <div>
                  <SharedStyled.Text color="grey" fontSize="12px">
                    <>
                      {salesPersonDrop?.find((person: any) => person._id === action.assignTo)?.name ||
                        '--'}
                    </>
                    &emsp;
                  </SharedStyled.Text>
                  <Pill
                    margin="0 0 0 10px"
                    numVal={action?.oppId ? action?.num : undefined}
                    path={
                      action?.oppId
                        ? `/${getEnumValue(action?.stageGroup)}/opportunity/${action?.oppId}`
                        : `/contact/profile/${action?.contactId}/false`
                    }
                    text={action?.oppId ? `${action?.PO}-${action?.num}` : 'Contact'}
                  />
                </div>
              </SharedStyled.FlexCol>
            </div>
          </div>
        </SharedStyled.FlexRow>

        <div>
          {!hasTodoCheckObj && (
            <RoundButton className="edit" onClick={handleEditClick}>
              <img src={EditIcon} alt="edit icon" />
            </RoundButton>
          )}
        </div>
      </SharedStyled.FlexRow>
    </div>
  )
}

export default ActionItem
